# 内容管理模块技术指南

## 概述

本项目的内容管理模块是一个基于 MDX 和 Contentlayer 的多语言内容管理系统，支持博客、产品页面和案例研究三种内容类型。该模块提供了智能的语言切换功能、类型安全的内容访问和自动化的 SEO 优化。

## 核心架构

### 技术栈
- **Contentlayer**: MDX 内容处理和类型生成
- **Next.js**: 静态生成和路由管理
- **TypeScript**: 类型安全和开发体验
- **next-intl**: 国际化支持

### 目录结构
```
src/
├── components/
│   ├── content/
│   │   └── language-versions.tsx      # 语言版本显示组件
│   └── locale/
│       └── content-language-indicator.tsx  # 内容语言指示器
├── lib/
│   └── content-language-utils.ts      # 核心工具函数
content/
├── blogs/
│   ├── en/*.mdx                       # 英文博客
│   └── zh/*.mdx                       # 中文博客
├── products/
│   ├── en/*.mdx                       # 英文产品页
│   └── zh/*.mdx                       # 中文产品页
└── case-studies/
    ├── en/*.mdx                       # 英文案例研究
    └── zh/*.mdx                       # 中文案例研究
```

## 核心组件详解

### 1. LanguageVersions 组件
**文件**: `src/components/content/language-versions.tsx`

**功能**:
- 显示当前内容的所有可用语言版本
- 提供智能语言切换功能
- 支持两种显示模式：紧凑型和完整型

**主要特性**:
- 自动检测内容类型和 slug
- 智能回退策略（内容不存在时跳转到列表页）
- 用户友好的反馈提示
- 响应式设计

**使用示例**:
```tsx
// 紧凑模式 - 水平按钮布局
<LanguageVersions compact={true} />

// 完整模式 - 详细卡片布局
<LanguageVersions compact={false} />
```

### 2. ContentLanguageIndicator 组件
**文件**: `src/components/locale/content-language-indicator.tsx`

**功能**:
- 提供内容语言可用性的视觉指示
- 显示当前内容标题
- 支持紧凑和完整两种变体

**主要特性**:
- 可用性状态图标（✓/✗）
- 当前语言高亮显示
- 智能按钮文本（Switch/Go to list）

### 3. 内容语言工具函数
**文件**: `src/lib/content-language-utils.ts`

这是整个模块的核心，包含以下主要函数：

#### `detectContentPage(pathname, locale)`
- **功能**: 从 URL 路径检测内容类型和 slug
- **输入**: 当前路径名和语言代码
- **输出**: 内容页面信息对象
- **示例**: `/zh/blogs/my-post` → `{ type: 'blog', slug: 'my-post', currentLocale: 'zh' }`

#### `contentExistsInLocale(contentType, slug, locale)`
- **功能**: 检查特定内容在目标语言中是否存在
- **实现**: 查询 Contentlayer 生成的数据集合
- **用途**: 语言切换前的可用性验证

#### `getAvailableLanguageVersions(contentType, slug, locales)`
- **功能**: 获取内容的所有语言版本信息
- **输出**: 包含可用性状态和 URL 的语言版本数组
- **用途**: UI 组件的数据源

#### `handleContentLanguageSwitch(pathname, currentLocale, targetLocale, locales)`
- **功能**: 智能语言切换的核心逻辑
- **策略**:
  - `direct`: 目标语言版本存在，直接跳转
  - `fallback-list`: 目标语言版本不存在，跳转到列表页
- **用户体验**: 确保用户永远不会遇到 404 错误

#### `generateContentUrl(contentType, slug, locale)`
- **功能**: 生成内容的标准化 URL
- **规则**: 
  - 英文（默认）: `/blogs/my-post`
  - 其他语言: `/zh/blogs/my-post`

#### `getContentTitle(contentType, slug, locale)`
- **功能**: 获取内容的实际标题
- **用途**: UI 显示和用户识别

## Contentlayer 配置

### 配置文件
**文件**: `contentlayer.config.ts`

### 核心特性
1. **统一的内容模式**: 所有内容类型共享相同的字段结构
2. **自动语言检测**: 从文件路径自动提取语言信息
3. **URL 自动生成**: 基于文件结构和 slug 生成一致的 URL
4. **类型安全**: 生成 TypeScript 类型定义

### 字段定义
```typescript
// 必需字段
title: string          // 内容标题
slug: string           // URL slug

// 可选字段
description?: string   // SEO 描述
coverImage?: string    // 封面图片
author?: string        // 作者
publishedAt?: Date     // 发布日期
featured?: boolean     // 是否为特色内容
tags?: string[]        // 标签

// 视频支持
videoUrl?: string      // 视频 URL
videoThumbnail?: string // 视频缩略图
videoDuration?: string  // 视频时长
```

### 计算字段
- `lang`: 从文件路径自动提取语言
- `url`: 自动生成完整 URL 路径
- `createdAt`: 文件创建时间

## 使用工作流

### 1. 内容创建
```bash
# 创建英文博客
content/blogs/en/my-new-post.mdx

# 创建中文博客
content/blogs/zh/wo-de-xin-wen-zhang.mdx
```

### 2. 内容结构
```mdx
---
title: "我的新文章"
slug: "wo-de-xin-wen-zhang"
description: "这是一篇关于..."
author: "作者名"
publishedAt: "2024-01-15"
featured: true
tags: ["技术", "教程"]
---

# 文章内容

这里是 MDX 内容...
```

### 3. 开发流程
```bash
# 启动开发服务器（自动监听内容变化）
pnpm dev

# 手动重新生成内容（如需要）
contentlayer build

# 生成 SEO 文件
pnpm generate:content
```

## 最佳实践

### 1. 内容组织
- 保持文件名和 slug 的一致性
- 使用有意义的目录结构
- 为每种语言创建对应的内容文件

### 2. 语言切换
- 始终提供回退策略
- 为用户提供清晰的反馈
- 保持 URL 结构的一致性

### 3. SEO 优化
- 为每个内容提供 description
- 使用适当的标签分类
- 设置合适的发布日期

### 4. 开发建议
- 利用 TypeScript 类型检查
- 使用 Contentlayer 生成的类型
- 遵循组件的 props 接口定义

## 扩展指南

### 添加新的内容类型
1. 在 `contentlayer.config.ts` 中定义新的文档类型
2. 创建对应的目录结构
3. 更新 `content-language-utils.ts` 中的类型定义
4. 添加相应的 URL 路由处理

### 添加新的语言
1. 在 `i18n/locale.ts` 中添加新的语言配置
2. 创建对应的内容目录
3. 更新 Next.js 国际化配置

这个内容管理模块为项目提供了强大而灵活的多语言内容管理能力，通过详细的代码注释和文档，初学者可以更好地理解和使用这个系统。
