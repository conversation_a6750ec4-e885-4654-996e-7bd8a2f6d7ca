# 服务架构迁移指南

## 概述

本文档说明了从单一工具文件 (`src/lib/content-language-utils.ts`) 迁移到按功能拆分的服务架构 (`src/services/content/`) 的变更。

## 迁移原因

### 原有架构的问题
- **单一文件过大**：400+ 行代码，职责混合
- **维护困难**：URL 生成、内容查询、语言切换逻辑都在一起
- **扩展性差**：添加新功能时文件会变得更加臃肿
- **测试困难**：难以独立测试特定功能

### 新架构的优势
- **职责分离**：每个服务模块专注于特定功能
- **易于维护**：修改某个功能时只需关注对应的服务文件
- **易于测试**：可以独立测试每个服务模块
- **易于扩展**：添加新功能时不会影响现有模块
- **CMS 迁移友好**：为未来迁移到 Headless CMS 提供了清晰的抽象层

## 新的服务架构

### 目录结构
```
src/services/content/
├── content-detection.ts    # URL 解析和内容检测
├── content-queries.ts      # 内容查询和元数据获取
├── language-switching.ts   # 智能语言切换逻辑
├── url-generation.ts       # URL 生成工具
├── types.ts               # 类型定义
└── index.ts               # 统一导出
```

### 服务模块说明

#### 1. content-detection.ts
- **职责**：URL 解析和内容类型检测
- **函数**：`detectContentPage()`, `getContentBasePath()`

#### 2. content-queries.ts
- **职责**：内容存在性检查和元数据获取
- **函数**：`contentExistsInLocale()`, `getContentTitle()`

#### 3. language-switching.ts
- **职责**：智能语言切换逻辑
- **函数**：`getAvailableLanguageVersions()`, `handleContentLanguageSwitch()`, `shouldShowLanguageSwitching()`

#### 4. url-generation.ts
- **职责**：URL 生成和管理
- **函数**：`generateContentUrl()`, `generateCanonicalUrl()`, `generateAlternateUrls()`

#### 5. types.ts
- **职责**：共享的 TypeScript 类型定义
- **类型**：`ContentType`, `ContentPageInfo`, `LanguageVersion`, `LanguageSwitchResult`

## 迁移变更

### 导入路径变更

#### 旧的导入方式
```typescript
import { 
  detectContentPage,
  handleContentLanguageSwitch,
  getAvailableLanguageVersions 
} from "@/lib/content-language-utils"
```

#### 新的导入方式
```typescript
// 推荐：统一导入
import { 
  detectContentPage,
  handleContentLanguageSwitch,
  getAvailableLanguageVersions 
} from "@/services/content"

// 或者：默认导入
import ContentService from "@/services/content"
```

### 函数签名变更

#### handleContentLanguageSwitch()
**旧签名**：
```typescript
handleContentLanguageSwitch(
  pathname: string,
  currentLocale: string,
  targetLocale: string,
  supportedLocales: string[]  // 已移除
)
```

**新签名**：
```typescript
handleContentLanguageSwitch(
  pathname: string,
  currentLocale: string,
  targetLocale: string
)
```

### 已更新的文件

以下文件已经更新为使用新的服务架构：

1. **组件文件**：
   - `src/components/locale/content-language-indicator.tsx`
   - `src/components/content/language-versions.tsx`
   - `src/components/locale/toggle.tsx`

2. **测试文件**：
   - `scripts/test-language-switching.mjs`

3. **文档文件**：
   - `docs/cms/MDX_CONTENT_GUIDE.md`
   - `docs/QUICK_REFERENCE.md`

## 向后兼容性

### 旧文件状态
- `src/lib/content-language-utils.ts` 已标记为 **DEPRECATED**
- 文件顶部添加了弃用警告和迁移指导
- 文件将在未来版本中移除

### 迁移建议
1. **立即行动**：更新所有导入路径到新的服务架构
2. **测试验证**：确保所有功能正常工作
3. **代码审查**：检查是否有遗漏的导入路径

## 未来扩展

### Headless CMS 集成准备
新的服务架构为未来迁移到 Headless CMS 提供了更好的基础：

1. **适配器模式**：可以轻松实现不同 CMS 的适配器
2. **接口稳定**：组件层无需修改
3. **数据源抽象**：服务层隔离了数据源的具体实现

### 添加新功能
当需要添加新的内容相关功能时：

1. 创建新的服务文件（如 `content-analytics.ts`）
2. 在 `index.ts` 中添加导出
3. 更新类型定义（如需要）
4. 编写对应的测试

## 总结

这次重构将内容管理功能从单一文件重构为按功能拆分的服务架构，提高了代码的可维护性、可测试性和可扩展性。同时为未来的 Headless CMS 迁移奠定了良好的基础。

所有现有功能保持不变，只是组织方式更加合理。开发者需要更新导入路径，但功能使用方式完全一致。
