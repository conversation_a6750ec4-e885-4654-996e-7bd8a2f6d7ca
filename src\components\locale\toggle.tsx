"use client";

import {
  Select,
  SelectContent,
  <PERSON>I<PERSON>,
  SelectTrigger,
} from "@/components/ui/select";
import { useParams, usePathname, useRouter } from "next/navigation";
import { toast } from "sonner";

import { MdLanguage } from "react-icons/md";
import { localeNames, locales } from "@/i18n/locale";
import {
  detectContentPage,
  handleContentLanguageSwitch
} from "@/lib/content-language-utils";

export default function ({ isIcon = false }: { isIcon?: boolean }) {
  const params = useParams();
  const locale = params.locale as string;
  const router = useRouter();
  const pathname = usePathname();

  const handleSwitchLanguage = (value: string) => {
    if (value !== locale) {
      // First detect if this is a content page
      const contentInfo = detectContentPage(pathname, locale);

      if (contentInfo.type === 'other') {
        // Non-content pages: use original simple logic
        let newPathName = pathname.replace(`/${locale}`, `/${value}`);
        if (!newPathName.startsWith(`/${value}`)) {
          newPathName = `/${value}${newPathName}`;
        }
        router.push(newPathName);
      } else {
        // Content pages: use intelligent switching logic
        const switchResult = handleContentLanguageSwitch(pathname, locale, value, locales);

        // Show user feedback for fallback scenarios first, then navigate
        if (switchResult.strategy === 'fallback-list') {
          toast.info(
            `This content is not available in ${localeNames[value]}. Redirected to the content list.`,
            {
              duration: 2000,
              position: 'top-center',
            }
          );
          // Navigate after showing the message
          setTimeout(() => {
            router.push(switchResult.url);
          }, 2000);
        } else {
          // Direct navigation - no delay needed
          router.push(switchResult.url);
        }
      }

      // Future: can add other page type handling here
      // else if (contentInfo.type === 'admin') { ... }
      // else if (contentInfo.type === 'special') { ... }
    }
  };

  return (
    <Select value={locale} onValueChange={handleSwitchLanguage}>
      <SelectTrigger className="flex items-center gap-2 border-none text-muted-foreground outline-hidden hover:bg-transparent focus:ring-0 focus:ring-offset-0">
        <MdLanguage className="text-xl" />
        {!isIcon && (
          <span className="hidden md:block">{localeNames[locale]}</span>
        )}
      </SelectTrigger>
      <SelectContent className="z-50 bg-background">
        {Object.keys(localeNames).map((key: string) => {
          const name = localeNames[key];
          return (
            <SelectItem className="cursor-pointer px-4" key={key} value={key}>
              {name}
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
}
