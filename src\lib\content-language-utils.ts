/**
 * DEPRECATED: This file is being refactored into services architecture
 *
 * This module is being split into focused service modules under src/services/content/:
 * - content-detection.ts - URL parsing and content type detection
 * - content-queries.ts - Content existence and title retrieval
 * - language-switching.ts - Intelligent language switching logic
 * - url-generation.ts - URL generation utilities
 * - types.ts - Shared type definitions
 * - index.ts - Unified exports
 *
 * Please import from @/services/content instead of this file.
 * This file will be removed in a future version.
 */

// Contentlayer Generated Data - Type-safe content collections
//
// FUTURE CMS MIGRATION NOTE:
// This is the primary integration point with Contentlayer. When migrating to a
// Headless CMS, this import should be replaced with a content adapter pattern:
//
// Example future implementation:
// import { contentAdapter } from '@/lib/content-adapter'
// const allBlogs = await contentAdapter.getAllBlogs()
// const allProducts = await contentAdapter.getAllProducts()
// const allCaseStudies = await contentAdapter.getAllCaseStudies()
//
// The adapter would handle different CMS backends (Strapi, Sanity, Contentful)
// while maintaining the same data structure and interface.
import { allBlogs, allProducts, allCaseStudies } from 'contentlayer/generated'

/**
 * Content types supported by the content management system
 *
 * - 'blog': Blog articles/posts
 * - 'product': Product pages/descriptions
 * - 'case-study': Case study documents
 * - 'other': Non-content pages (home, about, etc.)
 */
export type ContentType = 'blog' | 'product' | 'case-study' | 'other'

/**
 * Content page information extracted from pathname analysis
 *
 * This interface represents the result of analyzing a URL to determine
 * what type of content is being viewed and its identifying information.
 */
export interface ContentPageInfo {
  /** The type of content being viewed */
  type: ContentType
  /** The unique identifier (slug) for the content, null for list pages */
  slug: string | null
  /** The current locale/language of the page */
  currentLocale: string
}

/**
 * Language version information for a specific piece of content
 *
 * This interface represents the availability and access information
 * for a content item in a specific language.
 */
export interface LanguageVersion {
  /** The locale code (e.g., 'en', 'zh') */
  locale: string
  /** Whether the content exists in this language */
  exists: boolean
  /** The URL to access this language version */
  url: string
}

/**
 * Detect if the current pathname is a content page and extract information
 *
 * This function analyzes the current URL pathname to determine what type of content
 * is being viewed and extracts relevant information like content type and slug.
 * It handles both localized and non-localized URLs.
 *
 * URL Pattern Examples:
 * - '/blogs/my-post' -> { type: 'blog', slug: 'my-post', currentLocale: 'en' }
 * - '/zh/products/my-product' -> { type: 'product', slug: 'my-product', currentLocale: 'zh' }
 * - '/case-studies/' -> { type: 'case-study', slug: null, currentLocale: 'en' }
 * - '/about' -> { type: 'other', slug: null, currentLocale: 'en' }
 *
 * @param pathname - Current pathname from Next.js router
 * @param currentLocale - Current locale from URL parameters
 * @returns Content page information object
 */
export function detectContentPage(pathname: string, currentLocale: string): ContentPageInfo {
  // Remove locale prefix from pathname for consistent analysis
  // This normalizes URLs like '/zh/blogs/post' to '/blogs/post'
  let cleanPath = pathname
  if (currentLocale !== 'en' && pathname.startsWith(`/${currentLocale}`)) {
    cleanPath = pathname.replace(`/${currentLocale}`, '')
  }

  // Detect blog content pages
  // Matches URLs like '/blogs/my-post' or '/blogs/'
  if (cleanPath.startsWith('/blogs/')) {
    const slug = cleanPath.replace('/blogs/', '')
    return {
      type: 'blog',
      slug: slug || null,  // null for list pages like '/blogs/'
      currentLocale
    }
  }

  // Detect product content pages
  // Matches URLs like '/products/my-product' or '/products/'
  if (cleanPath.startsWith('/products/')) {
    const slug = cleanPath.replace('/products/', '')
    return {
      type: 'product',
      slug: slug || null,  // null for list pages like '/products/'
      currentLocale
    }
  }

  // Detect case study content pages
  // Matches URLs like '/case-studies/my-case-study' or '/case-studies/'
  if (cleanPath.startsWith('/case-studies/')) {
    const slug = cleanPath.replace('/case-studies/', '')
    return {
      type: 'case-study',
      slug: slug || null,  // null for list pages like '/case-studies/'
      currentLocale
    }
  }

  // Default case for non-content pages (home, about, contact, etc.)
  return {
    type: 'other',
    slug: null,
    currentLocale
  }
}

/**
 * Check if content exists in a specific language
 *
 * This function queries the Contentlayer generated data to determine if a specific
 * piece of content (identified by type and slug) has a translation available in
 * the target language. It's used to determine language availability before
 * attempting navigation.
 *
 * FUTURE CMS MIGRATION:
 * This function is a prime candidate for the adapter pattern. When migrating to
 * a Headless CMS, this would become:
 * ```typescript
 * return await contentAdapter.contentExists(contentType, slug, locale)
 * ```
 * The adapter would handle API calls, caching, and data transformation while
 * maintaining the same boolean return type.
 *
 * @param contentType - Type of content to check ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Target locale to check (e.g., 'en', 'zh')
 * @returns true if content exists in the target locale, false otherwise
 */
export function contentExistsInLocale(
  contentType: ContentType,
  slug: string,
  locale: string
): boolean {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return false
  }

  // Query the appropriate Contentlayer collection based on content type
  // NOTE: These direct array queries will be replaced with adapter calls in CMS migration
  switch (contentType) {
    case 'blog':
      // Search in the blogs collection for matching slug and language
      return allBlogs.some(blog => blog.slug === slug && blog.lang === locale)

    case 'product':
      // Search in the products collection for matching slug and language
      return allProducts.some(product => product.slug === slug && product.lang === locale)

    case 'case-study':
      // Search in the case studies collection for matching slug and language
      return allCaseStudies.some(caseStudy => caseStudy.slug === slug && caseStudy.lang === locale)

    default:
      // Unknown content type
      return false
  }
}

/**
 * Get all available language versions for a piece of content
 *
 * This function creates a comprehensive list of language versions for a specific
 * content item, including availability status and URLs for each supported locale.
 * It's used by UI components to display language switching options.
 *
 * The function checks each supported locale to see if the content exists in that
 * language and generates the appropriate URL for accessing it.
 *
 * @param contentType - Type of content ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param supportedLocales - Array of all supported locale codes
 * @returns Array of LanguageVersion objects with availability and URL info
 */
export function getAvailableLanguageVersions(
  contentType: ContentType,
  slug: string,
  supportedLocales: string[]
): LanguageVersion[] {
  // For non-content pages or missing slugs, mark all languages as unavailable
  if (contentType === 'other' || !slug) {
    return supportedLocales.map(locale => ({
      locale,
      exists: false,
      url: generateContentUrl(contentType, slug, locale)
    }))
  }

  // For each supported locale, check if content exists and generate URL
  return supportedLocales.map(locale => {
    // Check if this specific content exists in this language
    const exists = contentExistsInLocale(contentType, slug, locale)
    // Generate the URL for this language version
    const url = generateContentUrl(contentType, slug, locale)

    return {
      locale,
      exists,
      url
    }
  })
}

/**
 * Generate URL for content in a specific locale
 *
 * This function constructs the correct URL for accessing content in a specific
 * language, following the application's URL structure conventions. It handles
 * both individual content pages and list pages.
 *
 * URL Structure Examples:
 * - English (default): '/blogs/my-post', '/products/my-product'
 * - Other languages: '/zh/blogs/my-post', '/zh/products/my-product'
 * - List pages: '/blogs', '/zh/blogs'
 *
 * @param contentType - Type of content ('blog', 'product', 'case-study', 'other')
 * @param slug - Content identifier, empty string for list pages
 * @param locale - Target locale code (e.g., 'en', 'zh')
 * @returns Properly formatted URL string
 */
export function generateContentUrl(
  contentType: ContentType,
  slug: string,
  locale: string
): string {
  // Handle non-content pages (home, about, etc.)
  if (contentType === 'other') {
    return locale === 'en' ? '/' : `/${locale}`
  }

  // Get the base path for this content type
  const basePath = getContentBasePath(contentType)
  // English is the default locale and doesn't need a prefix
  const localePrefix = locale === 'en' ? '' : `/${locale}`

  // If no slug provided, return the list page URL
  if (!slug) {
    return `${localePrefix}${basePath}`
  }

  // Return the full content URL with locale prefix, base path, and slug
  return `${localePrefix}${basePath}/${slug}`
}

/**
 * Get base path for content type
 *
 * This helper function maps content types to their corresponding URL base paths.
 * It centralizes the URL structure configuration for easy maintenance.
 *
 * @param contentType - Type of content
 * @returns Base path string for the content type
 */
function getContentBasePath(contentType: ContentType): string {
  switch (contentType) {
    case 'blog':
      return '/blogs'
    case 'product':
      return '/products'
    case 'case-study':
      return '/case-studies'
    default:
      return ''
  }
}


/**
 * Handle intelligent language switching for content pages
 *
 * This is the core function for smart language switching. It attempts to navigate
 * users to the same content in a different language, but provides intelligent
 * fallback behavior when the content doesn't exist in the target language.
 *
 * Switching Strategies:
 * 1. 'direct': Content exists in target language - navigate directly to it
 * 2. 'fallback-list': Content doesn't exist - navigate to the content list page
 *
 * This approach ensures users never encounter 404 errors when switching languages,
 * instead providing them with relevant content in their preferred language.
 *
 * @param pathname - Current page pathname from Next.js router
 * @param currentLocale - Current language locale
 * @param targetLocale - Desired target language locale
 * @param supportedLocales - Array of all supported locale codes
 * @returns Object containing target URL, strategy used, and optional reason
 */
export function handleContentLanguageSwitch(
  pathname: string,
  currentLocale: string,
  targetLocale: string,
  supportedLocales: string[]
): {
  /** The URL to navigate to */
  url: string
  /** The strategy used for switching */
  strategy: 'direct' | 'fallback-list'
  /** Optional reason for the chosen strategy */
  reason?: string
} {
  // First, analyze the current page to understand what content we're viewing
  const contentInfo = detectContentPage(pathname, currentLocale)

  // Check if the same content exists in the target language
  const contentExists = contentInfo.slug && contentExistsInLocale(contentInfo.type, contentInfo.slug, targetLocale)

  // Strategy 1: Direct navigation - content exists in target language
  if (contentExists && contentInfo.slug) {
    return {
      url: generateContentUrl(contentInfo.type, contentInfo.slug, targetLocale),
      strategy: 'direct' as const
    }
  }

  // Strategy 2: Fallback to list page - content doesn't exist in target language
  // This ensures users still get relevant content in their preferred language
  const listUrl = generateContentUrl(contentInfo.type, '', targetLocale)

  return {
    url: listUrl,
    strategy: 'fallback-list' as const,
    reason: contentInfo.slug
      ? `Content "${contentInfo.slug}" not available in target language`
      : `Switched to ${targetLocale} content list`
  }
}

/**
 * Get content title for display purposes
 *
 * This function retrieves the actual title of a content item in a specific language
 * for display in UI components. It's useful for showing users what content they're
 * viewing or what content is available in different languages.
 *
 * The function queries the appropriate Contentlayer collection to find the content
 * item and extract its title. This provides a user-friendly way to identify content
 * across different languages.
 *
 * FUTURE CMS MIGRATION:
 * This function demonstrates the clean abstraction that makes CMS migration easier.
 * The interface (input parameters and return type) would remain identical, but the
 * implementation would change to:
 * ```typescript
 * return await contentAdapter.getContentTitle(contentType, slug, locale)
 * ```
 * This ensures UI components continue working without modification.
 *
 * @param contentType - Type of content ('blog', 'product', 'case-study')
 * @param slug - Unique identifier for the content
 * @param locale - Language locale for the content
 * @returns Content title string or null if not found
 */
export function getContentTitle(
  contentType: ContentType,
  slug: string,
  locale: string
): string | null {
  // Early return for non-content types or missing slugs
  if (contentType === 'other' || !slug) {
    return null
  }

  // Query the appropriate Contentlayer collection based on content type
  // NOTE: Direct array access will be replaced with adapter pattern for CMS migration
  switch (contentType) {
    case 'blog':
      // Find the blog post with matching slug and language
      const blog = allBlogs.find(b => b.slug === slug && b.lang === locale)
      return blog?.title || null

    case 'product':
      // Find the product with matching slug and language
      const product = allProducts.find(p => p.slug === slug && p.lang === locale)
      return product?.title || null

    case 'case-study':
      // Find the case study with matching slug and language
      const caseStudy = allCaseStudies.find(c => c.slug === slug && c.lang === locale)
      return caseStudy?.title || null

    default:
      // Unknown content type
      return null
  }
}
