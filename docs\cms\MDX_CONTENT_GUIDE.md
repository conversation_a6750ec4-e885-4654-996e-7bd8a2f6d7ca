# MDX 多语言内容生成模块使用指南

## 概述

本项目已成功集成了基于 MDX 的多语言内容管理系统，支持博客、产品和案例研究三种内容类型。该系统与现有的数据库驱动的 posts 系统并存，不会影响现有功能。

## 目录结构

```text
content/
├── blogs/
│   ├── en/
│   │   └── *.mdx
│   └── zh/
│       └── *.mdx
├── products/
│   ├── en/
│   │   └── *.mdx
│   └── zh/
│       └── *.mdx
└── case-studies/
    ├── en/
    │   └── *.mdx
    └── zh/
        └── *.mdx
```

## 页面路由

### 新增的 MDX 内容路由

- `/blogs` - 博客列表页
- `/blogs/[slug]` - 博客详情页
- `/products` - 产品列表页
- `/products/[slug]` - 产品详情页
- `/case-studies` - 案例研究列表页
- `/case-studies/[slug]` - 案例研究详情页

### 现有的数据库驱动路由（保持不变）

- `/posts` - 文章列表页
- `/posts/[slug]` - 文章详情页

## MDX 文件格式

每个 MDX 文件都需要包含 frontmatter 元数据：

```mdx
---
title: "文章标题"
slug: "url-slug"
description: "文章描述"
coverImage: "/imgs/cover.jpg"
author: "作者名称"
authorImage: "/imgs/author.jpg"
publishedAt: "2025-01-17"
featured: true
tags: ["标签1", "标签2"]
---

# 文章内容

这里是 MDX 格式的文章内容...
```

## 如何添加新内容

### 1. 创建 MDX 文件

在相应的目录中创建新的 MDX 文件：

```bash
# 博客文章
content/blogs/en/my-new-post.mdx
content/blogs/zh/wo-de-xin-wen-zhang.mdx

# 产品页面
content/products/en/new-product.mdx
content/products/zh/xin-chan-pin.mdx

# 案例研究
content/case-studies/en/case-study.mdx
content/case-studies/zh/an-li-yan-jiu.mdx
```

### 2. 编写内容

使用 MDX 格式编写内容，支持：

- Markdown 语法
- React 组件
- 自定义样式

### 3. 构建和部署

```bash
# 开发模式
pnpm dev

# 构建生产版本
pnpm build
```

## 技术栈

- **Contentlayer**: MDX 内容处理
- **Next.js**: 静态生成和路由
- **next-intl**: 国际化支持
- **TypeScript**: 类型安全

## 自动化功能

### SEO 优化

- 自动生成 sitemap.xml
- 自动生成 RSS feeds（支持多语言）
- 元数据优化

### 类型安全

- 类型安全的路由生成 (`src/lib/route-utils.ts`)
- 自定义 Link 组件
- TypeScript 支持

## 开发、测试和部署工作流

### 核心命令详解

#### 1. `contentlayer build`
**作用**：
- 处理 `content/` 目录下的所有 MDX 文件
- 将 MDX 内容转换为 TypeScript 类型安全的数据结构
- 生成 `.contentlayer/generated/` 目录，包含所有内容的索引和类型定义
- 支持多语言内容（en/zh）和多种内容类型（blogs、products、case-studies）

**何时运行**：
- 内容文件发生变化时
- 构建前必须运行
- 开发环境下自动监听文件变化

#### 2. `generate:sitemap`
**作用**：
- 生成 `public/sitemap.xml` 文件
- 包含所有静态页面和动态内容页面的 URL
- 支持多语言 SEO（hreflang 属性）
- 为搜索引擎提供网站结构信息

**依赖**：需要先运行 `contentlayer build` 来获取内容数据

#### 3. `generate:rss`
**作用**：
- 为每种语言生成 RSS 订阅源
- 英文：`public/rss.xml`
- 中文：`public/rss-zh.xml`
- 包含最新 20 篇博客文章的摘要信息

**依赖**：需要先运行 `contentlayer build` 来获取博客数据

#### 4. `generate:content`
**作用**：
- 组合命令，等同于 `generate:sitemap && generate:rss`
- 一次性生成所有 SEO 相关的静态文件

#### 5. `pnpm build`
**作用**：
- 完整的生产构建流程
- 执行顺序：`contentlayer build` → `generate:content` → `next build`
- 生成优化后的静态资源和服务器代码

#### 6. `pnpm dev`
**作用**：
- 启动开发服务器（使用 Turbopack 加速）
- 自动监听文件变化并热重载
- Contentlayer 在开发模式下自动处理内容变化

### 开发阶段工作流

```bash
# 1. 启动开发环境
pnpm dev

# 2. 编辑内容（自动处理）
# - 修改/添加 MDX 文件
# - Contentlayer 自动监听变化
# - 浏览器自动刷新

# 3. 手动更新 SEO 文件（可选）
pnpm generate:content
```

**流程图**：
```
启动开发 → pnpm dev → Contentlayer 自动监听 → 编辑 MDX → 自动重新生成 → 浏览器刷新
```

### 内容更新工作流

```bash
# 1. 添加/修改 MDX 内容文件
# content/blogs/en/new-post.mdx
# content/blogs/zh/xin-wen-zhang.mdx

# 2. 重新生成内容索引（开发环境自动）
contentlayer build

# 3. 更新 SEO 文件
pnpm generate:content

# 4. 测试本地效果
pnpm dev

# 5. 构建生产版本
pnpm build
```

### 生产部署工作流

```bash
# 完整构建命令（推荐）
pnpm build

# 或者分步执行
contentlayer build      # 处理 MDX 内容
pnpm generate:sitemap   # 生成站点地图
pnpm generate:rss       # 生成 RSS 订阅
next build              # 构建 Next.js 应用

# 启动生产服务器
pnpm start
```

**流程图**：
```
准备部署 → contentlayer build → generate:content → next build → 部署到服务器
```

### CI/CD 部署工作流

```yaml
# 示例 GitHub Actions 工作流
name: Deploy
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'

      # 安装依赖
      - run: pnpm install

      # 完整构建（包含所有步骤）
      - run: pnpm build

      # 部署到服务器
      - run: pnpm deploy
```

### 开发命令速查

```bash
# === 开发环境 ===
pnpm dev                    # 启动开发服务器
pnpm dev --turbo           # 使用 Turbopack（默认已启用）

# === 内容处理 ===
contentlayer build         # 处理 MDX 内容
pnpm generate:content      # 生成 SEO 文件
pnpm generate:sitemap      # 仅生成 sitemap
pnpm generate:rss          # 仅生成 RSS

# === 构建部署 ===
pnpm build                 # 完整生产构建
pnpm start                 # 启动生产服务器
pnpm analyze               # 分析构建包大小

# === 数据库相关 ===
pnpm db:generate           # 生成数据库迁移
pnpm db:migrate            # 执行数据库迁移
pnpm db:studio             # 启动数据库管理界面
pnpm db:push               # 推送数据库变更

# === 代码质量 ===
pnpm lint                  # 代码检查
```

### 测试工作流

#### 本地测试

```bash
# 1. 启动开发服务器
pnpm dev

# 2. 测试内容渲染
# 访问 http://localhost:3000/blogs
# 访问 http://localhost:3000/products
# 访问 http://localhost:3000/case-studies

# 3. 测试多语言切换
# 访问 http://localhost:3000/zh/blogs

# 4. 测试 SEO 文件生成
pnpm generate:content
# 检查 public/sitemap.xml
# 检查 public/rss.xml 和 public/rss-zh.xml
```

#### 构建测试

```bash
# 1. 完整构建测试
pnpm build

# 2. 启动生产服务器测试
pnpm start

# 3. 检查构建输出
# .next/static/ - 静态资源
# .next/server/ - 服务器代码
# public/ - 公共文件（sitemap, rss）
```

#### 内容验证清单

- [ ] MDX 文件 frontmatter 格式正确
- [ ] 所有必需字段已填写（title, slug）
- [ ] 多语言版本内容一致
- [ ] 图片路径正确且可访问
- [ ] 标签和分类合理
- [ ] 发布日期格式正确（YYYY-MM-DD）
- [ ] SEO 描述长度适中（150-160字符）

### 最佳实践

#### 内容管理

1. **统一命名规范**
   ```bash
   # 文件命名：使用连字符分隔
   my-awesome-blog-post.mdx

   # Slug 命名：与文件名保持一致
   slug: "my-awesome-blog-post"
   ```

2. **多语言一致性**
   ```bash
   # 英文版本
   content/blogs/en/getting-started.mdx
   slug: "getting-started"

   # 中文版本
   content/blogs/zh/getting-started.mdx
   slug: "getting-started"  # 保持相同的 slug
   ```

3. **图片管理**
   ```bash
   # 推荐路径结构
   public/images/blogs/post-name/cover.jpg
   public/images/products/product-name/hero.jpg
   ```

#### 性能优化

1. **图片优化**
   - 使用 WebP 格式
   - 提供多种尺寸
   - 添加 alt 属性

2. **内容分页**
   - 博客列表分页显示
   - 限制首页显示数量

3. **缓存策略**
   - 静态生成页面自动缓存
   - CDN 分发静态资源

#### SEO 优化

1. **元数据完整性**
   ```mdx
   ---
   title: "具体而有吸引力的标题"
   description: "150-160字符的精准描述"
   coverImage: "/images/optimized-cover.jpg"
   tags: ["相关", "标签", "不超过5个"]
   ---
   ```

2. **URL 结构**
   - 使用语义化的 slug
   - 保持 URL 简洁明了
   - 多语言 URL 一致性

3. **内容质量**
   - 原创高质量内容
   - 合理的标题层级（H1-H6）
   - 内部链接建设

### 故障排除

#### 常见问题

1. **Contentlayer 构建失败**
   ```bash
   # 清理缓存重新构建
   rm -rf .contentlayer
   pnpm contentlayer build
   ```

2. **SEO 文件未更新**
   ```bash
   # 手动重新生成
   pnpm generate:content
   ```

3. **开发服务器热重载失效**
   ```bash
   # 重启开发服务器
   pnpm dev
   ```

4. **类型错误**
   ```bash
   # 重新生成类型定义
   pnpm contentlayer build
   ```

#### 调试技巧

1. **检查生成的内容**
   ```bash
   # 查看生成的内容索引
   cat .contentlayer/generated/index.mjs
   ```

2. **验证 frontmatter**
   ```bash
   # 使用 YAML 验证器检查格式
   ```

3. **测试路由**
   ```bash
   # 检查 Next.js 路由生成
   pnpm build --debug
   ```

## 组件使用

### MDX 渲染组件

```tsx
import { Mdx } from '@/components/mdx'

<Mdx code={content.body.code} />
```

### 类型安全链接

```tsx
import { SafeLink, BlogLink, ProductLink } from '@/components/ui/safe-link'

<BlogLink slug="my-post">阅读文章</BlogLink>
<ProductLink slug="my-product">查看产品</ProductLink>
```

### 语言切换组件

项目提供了两个专门的语言切换组件，用于不同的使用场景：

#### 1. ContentLanguageIndicator - 页头语言指示器

**用途**：专门用于内容详情页的页头，提供紧凑的语言切换界面。

**当前使用位置**：
- 博客详情页：`/blogs/[slug]`
- 产品详情页：`/products/[slug]`
- 案例研究详情页：`/case-studies/[slug]`

**使用示例**：
```tsx
import ContentLanguageIndicator from '@/components/locale/content-language-indicator'

// 在页头使用（紧凑模式）
<div className="flex items-center justify-between">
  <div className="flex items-center gap-2">
    {/* 标签和元数据 */}
  </div>
  <ContentLanguageIndicator variant="compact" />
</div>

// 在专门的语言选择区域使用（完整模式）
<ContentLanguageIndicator variant="full" className="mb-4" />
```

**特征**：
- 使用 Material Design 图标
- 显示 ✓ (绿色) / ✗ (红色) 可用性状态图标
- 紧凑设计，适合页头放置
- 语言按钮带有清晰的视觉反馈

#### 2. LanguageVersions - 详细语言版本组件

**用途**：设计用于内容区域和侧边栏，提供详细的语言版本信息。

**适用场景**：
- 内容侧边栏
- 内容管理界面
- 需要显示内容标题的场合
- 管理面板或详细语言选择

**使用示例**：
```tsx
import LanguageVersions from '@/components/content/language-versions'

// 在侧边栏使用（完整卡片模式）
<aside className="w-64">
  <LanguageVersions compact={false} />
</aside>

// 在内容区域使用（紧凑模式）
<div className="content-footer">
  <LanguageVersions compact={true} />
</div>
```

**特征**：
- 使用 Lucide React 图标
- 显示内容类型徽章（文章、产品、案例研究）
- 显示每种语言的实际内容标题
- 更宽敞的设计，包含详细信息
- 包含帮助提示和指导文本

#### 组件选择指南

| 使用场景 | 推荐组件 | 原因 |
|---------|---------|------|
| 页头/导航栏 | ContentLanguageIndicator | 紧凑设计，状态图标清晰 |
| 侧边栏 | LanguageVersions | 详细信息，显示内容标题 |
| 内容管理界面 | LanguageVersions | 全面的语言版本信息 |
| 移动端 | ContentLanguageIndicator | 节省空间，简洁明了 |

## 配置文件

### contentlayer.config.ts

定义内容类型和字段结构

### next.config.mjs

集成 Contentlayer 和 MDX 支持

### scripts/

包含 SEO 自动化脚本

## 智能语言切换功能

### 功能概述

项目已实现智能语言切换功能，为多语言内容提供无缝的用户体验：

1. **增强的全局语言切换器**：位于头部导航栏
2. **内容页面语言版本指示器**：显示在内容详情页面右上角
3. **智能切换逻辑**：根据目标语言版本是否存在选择跳转策略

### 切换策略

#### 1. 直接切换 (Direct)
- **场景**：目标语言版本存在
- **行为**：直接跳转到对应语言版本
- **示例**：`/blogs/getting-started-with-shipany` → `/zh/blogs/getting-started-with-shipany`

#### 2. 列表页降级 (Fallback to List)
- **场景**：目标语言版本不存在
- **行为**：跳转到目标语言的内容列表页
- **示例**：`/blogs/english-only-test` → `/zh/blogs`
- **提示**：显示内容不可用的友好提示

#### 3. 首页降级 (Fallback to Home)
- **场景**：非内容页面
- **行为**：跳转到目标语言的首页

### 语言版本指示器

在博客、产品、案例研究详情页面，右上角会显示 `ContentLanguageIndicator` 组件：

- ✅ **绿色勾号**：该语言版本可用
- ❌ **红色叉号**：该语言版本不可用
- **当前语言**：高亮显示
- **点击切换**：可直接切换到对应语言版本

> 💡 **组件说明**：这是 `ContentLanguageIndicator` 组件的实际应用，详细使用方法请参考上面的"语言切换组件"章节。

### 多语言内容管理最佳实践

#### 1. 保持 slug 一致性
```bash
# 推荐的文件结构
content/blogs/en/getting-started.mdx     # 英文版本
content/blogs/zh/getting-started.mdx     # 中文版本

# 两个版本使用相同的 slug
slug: "getting-started"
```

#### 2. 单语言内容处理
```bash
# 如果某些内容只有一种语言
content/blogs/en/english-only-article.mdx  # 仅英文
# 用户切换到中文时会自动跳转到中文博客列表页
```

#### 3. 内容同步检查
- 定期检查多语言版本的内容是否同步
- 确保重要信息在所有语言版本中都有体现
- 使用相同的标签和分类

### 测试语言切换功能

#### 测试场景 1：完整双语内容
1. 访问：`/blogs/getting-started-with-shipany`
2. 点击头部语言切换器选择中文
3. 应该跳转到：`/zh/blogs/getting-started-with-shipany`
4. 无提示信息，无缝切换

#### 测试场景 2：单语言内容降级
1. 访问：`/blogs/english-only-test`
2. 点击头部语言切换器选择中文
3. 应该跳转到：`/zh/blogs`
4. 显示提示：内容不可用，已重定向到列表页

#### 测试场景 3：语言版本指示器
1. 在任意内容详情页面查看右上角
2. 观察语言版本状态（绿色勾号/红色叉号）
3. 点击可用的语言版本进行切换

## 注意事项

1. **与现有系统共存**: 新的 MDX 系统不会影响现有的 posts 功能
2. **统一命名规范**: 所有内容类型使用复数形式和连字符分隔
3. **多语言支持**: 每种语言需要单独的 MDX 文件
4. **静态生成**: 所有 MDX 内容在构建时生成静态页面
5. **SEO 友好**: 自动生成 sitemap 和 RSS feeds

## 示例内容

项目中已包含以下示例内容：

- 英文博客：`getting-started-with-shipany.mdx`
- 中文博客：`shipany-kuai-su-ru-men.mdx`
- 英文产品：`ai-content-generator.mdx`
- 中文产品：`ai-nei-rong-sheng-cheng-qi.mdx`
- 英文案例：`techcorp-ai-transformation.mdx`
- 中文案例：`keji-gongsi-ai-zhuanxing.mdx`

## 扩展功能

系统设计为可扩展的，可以轻松添加：

- 新的内容类型
- 更多语言支持
- 自定义 MDX 组件
- 高级 SEO 功能

## 支持

如有问题，请参考：

1. Contentlayer 官方文档
2. Next.js 文档
3. MDX 文档
4. 项目中的示例代码
